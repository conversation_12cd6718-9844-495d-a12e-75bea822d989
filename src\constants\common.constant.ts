import { DataAreaSettingType } from "@/features/area-setting/type";
import { IMerchantCoreType } from "@/features/auth";

// Import payment method images
import PayPayIcon from "/images/group_1261.svg";
import DPayIcon from "/images/group_1263.svg";
import AuPayIcon from "/images/group_1265.svg";
import MerPayIcon from "/images/group_1268.svg";
import WeChatPayIcon from "/images/group_1270.svg";
import AliPayIcon from "/images/group_1272.svg";
import BankPayIcon from "/images/group_1276.png";
import TrafficIC1Icon from "/images/group_1278.svg";
import TrafficIC2Icon from "/images/group_1280.svg";
import TrafficIC3Icon from "/images/group_1282.svg";
import TrafficIC4Icon from "/images/group_1284.svg";
import TrafficIC5Icon from "/images/group_1286.svg";
import TrafficIC6Icon from "/images/group_1288.svg";
import TrafficIC7Icon from "/images/group_1292.svg";
import TrafficIC8Icon from "/images/group_1294.svg";
import TrafficIC9Icon from "/images/group_1296.svg";
import WaonIcon from "/images/group_1299.svg";
import UnionPayIcon from "/images/group_1300.svg";
import IDIcon from "/images/group_1301.svg";
import NanacoIcon from "/images/group_1308.svg";
import EdyIcon from "/images/group_1310.svg";
import QuicPayIcon from "/images/group_1312.svg";

export const SITEKEY = '6LdKuUYhAAAAAGzSRvwNjE-LAKAaRkcP6Hyo8Pmc';

export const mapAccountType = new Map([[*********, "普通"], [*********, "当座"]]);
export const mapBusinessType = new Map([[*********, "クリニック"], [*********, "薬局"], [*********, "歯科"], [*********, "病院（20-199床）"], [*********, "病院（200床以上）"], [*********, "薬局（年商100億円以上）"], [*********, "B2B"], [*********, "クリニック(海外)"], [*********, "薬局（中小企業）"]]);
export const mapBusinessForm = new Map([[*********, "法人"], [*********, "個人"]]);


export const mapTransactionType = new Map([
    [*********, "クレジットカード一括"]
    , [*********, "クレジットカードボーナス"]
    , [*********, "クレジットカード分割"]
    , [*********, "クレジットカードリボ"]
    , [*********, "銀聯"]
    , [*********, "交通系電子マネー"]
    , [*********, "WAON"]
    , [*********, "nanaco"]
    , [*********, "Edy"]
    , [*********, "iD"]
    , [*********, "PayPay"]
    , [*********, "メルペイ"]
    , [*********, "auPAY"]
    , [*********, "LinePay"]
    , [*********, "d払い"]
    , [*********, "アリペイ"]
    , [*********, "WeChatPay"]
    , [*********, "BankPay"]
    , [*********, "クレジットカード一括(JCB)"]
    , [*********, "クレジットカード2回払(JCB)"]
    , [*********, "クレジットカードボーナス(JCB)"]
    , [*********, "クレジットカード分割(JCB)"]
    , [*********, "クレジットカードリボ(JCB)"]
    , [*********, "JCBプレモ"]
    , [*********, "QUICPay"]
]);

// Enum for status codes
export enum OtherMeasuresStatus {
    IMPLEMENTED = *********,
    NOT_PLANNED = *********,
    PLANNED = *********,
}

export const STEP = {
    HOUJIN: 1,
    DAIHYOUSHA: 2,
    SHOPINFO: 3,
    BANK: 4,
    ADDITIONAL: 5,
    ADDITIONAL1: 6,
    CHOQIPAY: 7,
    KIYAKU: 8,
    CONFIRM: 9,
    COMPLETE: 10,
} as const;

export enum MAIL_REGISTER {
    SEND_MAIL = 1,
    SEND_MAIL_SUCCESS = 2,
}

export enum REGISTER {
    PASSWORD = 1,
    BUSINESS_TYPE = 2,
    ENTITY_TYPE = 3,
    MEDICAL_CODE = 4,
    REFERRAL_CODE = 5,
    CONFIRMATION = 6,
    SUCCESS = 7,
}

export enum ERROR_CODE {
    TOKEN_EXPIRED = 43,
    ACCOUNT_ALREADY_EXISTS = 47,
    INVALID_REQUEST = 417,
    SERVER_ERROR = 500
}

// Admin transaction type mapping (string keys)
export const adminMapTransactionType = new Map([
    ['1', 'クレジット'],
    ['2', 'デビット'],
    ['3', 'プリペイド'],
    ['4', 'QRコード'],
    ['5', 'その他']
]);

export const merchantCore:IMerchantCoreType  = {
    agxMerchantNo: null,
    agxMerchantid: '',
    agxContactId: '',
    agxBusinessType: *********,
    agxBusinessForm: *********,
    agxMedicalInstitutionCode: '',
    agxEmsEmployeeNo: '',
    agxCorporateName: '',
    agxCorporatePhoneticName: '',
    agxCorporateEnglishName: '',
    agxCorporateNumber: '',
    agxCorporatePostalCode: '',
    agxCorporatePrefecture: '', //北海道
    agxCorporateAddress1: '',
    agxCorporateAddress2: '',
    agxCorporatePhoneticPrefecture: '',
    agxCorporatePhoneticAddress1: '',
    agxCorporatePhoneticAddress2: '',
    agxCorporatePhoneNumber: '',
    agxCorporateFaxNumber: '',
    agxRepresentativeName: '',
    agxRepresentativePhoneticName: '',
    agxRepresentativeGender: *********,
    agxRepresentativeBirthday: '',
    agxRepresentativeAddressCopyFlag: false,
    agxRepresentativePostalCode: '',
    agxRepresentativePrefecture: '', //北海道
    agxRepresentativeAddress1: '',
    agxRepresentativeAddress2: '',
    agxRepresentativePhoneticPrefecture: '',
    agxRepresentativePhoneticAddress1: '',
    agxRepresentativePhoneticAddress2: '',
    agxRepresentativePhoneNumber: '',
    agxRepresentativeFaxNumber: '',
    agxStoreName: '',
    agxStorePhoneticName: '',
    agxStoreEnglishName: '',
    agxUrl: '',
    agxBrandName: '',
    agxBusinessDate: '',
    agxRegularHoliday: '',
    agxBusinesssHours: '',
    agxStoreAddressCopyFlag1: false,
    agxStoreAddressCopyFlag2: false,
    agxStorePostalCode: '',
    agxStorePrefecture: '',
    agxStoreAddress1: '',
    agxStoreAddress2: '',
    agxStorePhoneticPrefecture: '',
    agxStorePhoneticAddress1: '',
    agxStorePhoneticAddress2: '',
    agxStorePhoneNumber: '',
    agxStoreFaxNumber: '',
    agxBankNo: '',
    agxBankName: '',
    agxBankType: *********,
    agxBranchNo: '',
    agxBranchName: '',
    agxBranchType: *********,
    agxBankPhoneticName: '',
    agxBranchPhoneticName: '',
    agxAccountType: *********,
    agxAccountNo: '',
    agxAccountHolder: '',
    agxContactName: '',
    agxContactEmail: '',
    agxContactPhoneticName: '',
    agxContactPhoneNumber: '',
    agxCapital: 0,
    agxNumberOfEmployees: '',
    agxFoundingDate: null,
    agxMonthlySales: 0,
    agxDoorToDoorSales: false,
    agxTelemarketingSales: false,
    agxPyramidScheme: false,
    agxBusinessOpportunityRelatedSales: false,
    agxSpecifiedContinuousServices: false,
    agxCardInformationRetentionStatus: *********,
    agxNoRetainingCardInfoDate: null,
    agxPcidssStatus: *********,
    agxPcidssExpectedComplianceDate: null,
    agxThreeDSecureStatus: *********,
    agxThreeDSecureDate: null,
    agxSecurityCodeCheckStatus: *********,
    agxSecurityCodeCheckDate: null,
    agxIllegalDeliveryDestinationStatus: *********,
    agxIllegalDeliveryDestinationDate: null,
    agxBehaviorAnalysisStatus: *********,
    agxBehaviorAnalysisDate: null,
    agxOtherMeasuresStatus: *********,
    agxOtherMeasuresDate: null,
    agxOtherMeasuresDescription: '',
    agxNumberOfTerminal: 1,
    agxColorOfTerminal: *********,
    agxSettlementCard: true,
    agxSettlementJcb: true,
    agxSettlementTraffic: false,
    agxSettlementNanaco: false,
    agxSettlementWaon: false,
    agxSettlementEdy: false,
    agxSettlementAid: false,
    agxSettlementQuicpay: false,
    agxSettlementQrCode: false,
    memberType: null,
    agxSettlementPackage1: false,
    agxSettlementPackage2: false,
    agxApplicationStatus: null
};

export const dataAreaSettingInit: DataAreaSettingType = {
  agxAreas: [],
  agxSubAreas: [],
  agxSubAreaModal: [],
  loading: true,
  error: false
}

// Transaction codes for payment processing
export const TransactionCode = {
    売上: '0',
    取消: '1',
    返品: '2',
    处理未了: '3'
} as const;

// Transaction types for different payment methods
export const TransactionType = {
    クレジットカード: '2',
    iD: '4',
    QUICPay: '5',
    交通系電子マネー: '6',
    銀聯: '7',
    WAON: '8',
    Edy: '9',
    nanaco: 'A',
    QRコード: 'B'
} as const;

// Transaction categories for credit card payments
export const TransactioCategory = {
    クレジットカード一括: '10',
    クレジットカードボーナス: '2x',
    クレジットカード分割: '6x',
    クレジットカードリボ: '80',
} as const;

// Map for transaction code payment data
export const mapTransactionCodePaymentData = new Map([
    [0, "売上"],
    [1, "取消"],
    [2, "处理未了"]
]);

// Map for credit card transaction types
export const mapTransactionTypeCredit = new Map([
    ['10', "クレジットカード一括"],

    ['20', "クレジットカードボーナス"],
    ['21', "クレジットカードボーナス"],
    ['22', "クレジットカードボーナス"],
    ['23', "クレジットカードボーナス"],
    ['24', "クレジットカードボーナス"],
    ['25', "クレジットカードボーナス"],
    ['26', "クレジットカードボーナス"],
    ['27', "クレジットカードボーナス"],
    ['28', "クレジットカードボーナス"],
    ['29', "クレジットカードボーナス"],

    ['60', "クレジットカード分割"],
    ['61', "クレジットカード分割"],
    ['62', "クレジットカード分割"],
    ['63', "クレジットカード分割"],
    ['64', "クレジットカード分割"],
    ['65', "クレジットカード分割"],
    ['66', "クレジットカード分割"],
    ['67', "クレジットカード分割"],
    ['68', "クレジットカード分割"],
    ['69', "クレジットカード分割"],

    ['80', "クレジットカードリボ"]
]);

// Map for other transaction types
export const mapTransactionTypeOther = new Map([
    ['2', "デビット"],
    ['4', "iD"],
    ['5', "QUICPay"],
    ['6', "交通系電子マネー"],
    ['7', "銀聯"],
    ['8', "WAON"],
    ['9', "Edy"],
    ['A', "nanaco"],
    ['B', "QRコード"]
]);
export const mapTransactionImage = new Map<number, string[]>([
  [*********, []], // クレジットカード一括
  [*********, []], // クレジットカードボーナス
  [*********, []], // クレジットカード分割
  [*********, []], // クレジットカードリボ
  [*********, [UnionPayIcon]], // 銀聯
  [*********, [
    TrafficIC1Icon, // threedot1dot1
    TrafficIC2Icon, // threedot1dot2
    TrafficIC3Icon, // threedot1dot3
    TrafficIC4Icon, // threedot1dot4
    TrafficIC5Icon, // threedot1dot5
    TrafficIC6Icon, // threedot2dot1
    TrafficIC7Icon, // threedot2dot2
    TrafficIC8Icon, // threedot2dot3
    TrafficIC9Icon, // threedot2dot4
  ]],
  [*********, [WaonIcon]], // WAON
  [*********, [NanacoIcon]], // nanaco
  [*********, [EdyIcon]], // Edy
  [*********, [IDIcon]], // iD
  [*********, [PayPayIcon]], // PayPay
  [*********, [MerPayIcon]], // メルペイ
  [*********, [AuPayIcon]], // auPAY
  [*********, []], // LinePay (chưa có icon)
  [*********, [DPayIcon]], // d払い
  [*********, [AliPayIcon]], // アリペイ
  [*********, [WeChatPayIcon]], // WeChatPay
  [*********, [BankPayIcon]], // BankPay
  [*********, []], // クレジットカード一括(JCB)
  [*********, []], // クレジットカード2回払(JCB)
  [*********, []], // クレジットカードボーナス(JCB)
  [*********, []], // クレジットカード分割(JCB)
  [*********, []], // クレジットカードリボ(JCB)
  [*********, []], // JCBプレモ
  [*********, [QuicPayIcon]], // QUICPay
]);
