import React from 'react';
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table';
import { Card, CardContent } from '@/components/ui/card';
import { formatNumber } from '@/utils/dateUtils';

interface TaxSummaryData {
  subTotalNonTax: number;
  subTotalInclTax10: number;
  subTotalConsumptionTax: number;
  subTotalTaxIncl: number;
}

interface TaxSummaryTableProps {
  data: TaxSummaryData;
  transferDate: string;
  switchLayoutDate: string;
  useCardStyle?: boolean; // true for DepositTables.tsx style, false for DepositPage.tsx style
}

export const TaxSummaryTable: React.FC<TaxSummaryTableProps> = ({
  data,
  transferDate,
  switchLayoutDate
}) => {
  return (
    <>
      {transferDate > switchLayoutDate && (
        <div className="flex justify-end text-[#6F6F6E]">
          <div className="w-80">
            <Table className="w-full">
              <TableBody>
                <TableRow className="border-b border-[#6F6F6E]">
                  <TableCell className="px-1 py-3 text-[#6F6F6E] text-xl">非課税小計</TableCell>
                  <TableCell className="px-1 py-3 text-right text-[#6F6F6E] text-xl">
                    {data.subTotalNonTax !== null && data.subTotalNonTax !== undefined ? formatNumber(data.subTotalNonTax) : ''}円
                  </TableCell>
                </TableRow>
                <TableRow className="border-b border-[#6F6F6E]">
                  <TableCell className="px-1 py-3 text-[#6F6F6E] text-xl">10%小計（税込）</TableCell>
                  <TableCell className="px-1 py-3 text-right text-[#6F6F6E] text-xl">
                    {data.subTotalInclTax10 !== null && data.subTotalInclTax10 !== undefined ? formatNumber(data.subTotalInclTax10) : ''}円
                  </TableCell>
                </TableRow>
                <TableRow className="border-b border-[#6F6F6E]">
                  <TableCell className="px-1 py-3 text-[#6F6F6E] text-xl">内消費税額</TableCell>
                  <TableCell className="px-1 py-3 text-right text-[#6F6F6E] text-xl">
                    {data.subTotalConsumptionTax !== null && data.subTotalConsumptionTax !== undefined ? formatNumber(data.subTotalConsumptionTax) : ''}円
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="px-1 py-3 text-[#6F6F6E] text-xl">合計（税込）</TableCell>
                  <TableCell className="px-1 py-3 text-right text-[#6F6F6E] text-xl">
                    {data.subTotalTaxIncl !== null && data.subTotalTaxIncl !== undefined ? formatNumber(data.subTotalTaxIncl) : ''}円
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </div>
      )}
    </>
  );
};
