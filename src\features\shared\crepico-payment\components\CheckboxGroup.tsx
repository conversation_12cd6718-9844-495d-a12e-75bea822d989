import React, { ChangeEvent } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';

// Reusable checkbox item component
interface CheckboxItemProps {
    id: string;
    value: string;
    label: string;
    checked: boolean;
    onChange: (e: ChangeEvent<HTMLInputElement>) => void;
}

export const CheckboxItem: React.FC<CheckboxItemProps> = ({ 
    id, 
    value, 
    label, 
    checked, 
    onChange 
}) => (
    <div className="flex items-center space-x-2">
        <Checkbox
            id={id}
            checked={checked}
            className="w-5 h-5 data-[state=checked]:bg-[#37ae9c] data-[state=checked]:border-[#37ae9c] border-[#6F6F6E]"
            onCheckedChange={(checkedState) => {
                const syntheticEvent = {
                    target: { value, checked: checkedState === true }
                } as ChangeEvent<HTMLInputElement>;
                onChange(syntheticEvent);
            }}
        />
        <Label htmlFor={id} className="cursor-pointer">
            {label}
        </Label>
    </div>
);

// Payment status checkbox group - Unified version for both Store and AdminStore
interface PaymentStatusGroupProps {
    paymentStatus: string[];
    onChange: (e: ChangeEvent<HTMLInputElement>) => void;
    TransactionCode: any;
    hideReturn?: boolean; // Optional prop to hide "返品" checkbox for AdminStore
}

export const PaymentStatusGroup: React.FC<PaymentStatusGroupProps> = ({
    paymentStatus,
    onChange,
    TransactionCode,
    hideReturn = false
}) => (
    <div className="flex flex-wrap gap-4 md:gap-14 md:flex-row flex-col">
        <CheckboxItem
            id="payment-status-earnings"
            value={TransactionCode.売上}
            label="売上"
            checked={paymentStatus?.includes(TransactionCode.売上)}
            onChange={onChange}
        />
        <CheckboxItem
            id="payment-status-cancel"
            value={TransactionCode.取消}
            label="取消"
            checked={paymentStatus?.includes(TransactionCode.取消)}
            onChange={onChange}
        />
        {!hideReturn && (
            <CheckboxItem
                id="payment-status-return"
                value={TransactionCode.返品}
                label="返品"
                checked={paymentStatus?.includes(TransactionCode.返品)}
                onChange={onChange}
            />
        )}
        <CheckboxItem
            id="payment-status-unfinished-processing"
            value={TransactionCode.处理未了}
            label="処理未了"
            checked={paymentStatus?.includes(TransactionCode.处理未了)}
            onChange={onChange}
        />
    </div>
);

// Combined Transaction Category and Payment Types checkbox group
interface CombinedTransactionGroupProps {
    transactionCategory: string[];
    paymentTypes: string[];
    onChange: (e: ChangeEvent<HTMLInputElement>) => void;
    TransactioCategory: any;
    TransactionType: any;
}

export const CombinedTransactionGroup: React.FC<CombinedTransactionGroupProps> = ({
    transactionCategory,
    paymentTypes,
    onChange,
    TransactioCategory,
    TransactionType
}) => (
    <div className="grid xl:grid-cols-2 2xl:grid-cols-3 gap-4">
        {/* Transaction Category Items */}
        <CheckboxItem
            id="transaction-category-credit-lump"
            value={TransactioCategory.クレジットカード一括}
            label="クレジットカード一括"
            checked={transactionCategory?.includes(TransactioCategory.クレジットカード一括)}
            onChange={onChange}
        />
        <CheckboxItem
            id="transaction-category-credit-bonus"
            value={TransactioCategory.クレジットカードボーナス}
            label="クレジットカードボーナス"
            checked={transactionCategory?.includes(TransactioCategory.クレジットカードボーナス)}
            onChange={onChange}
        />
        <CheckboxItem
            id="transaction-category-credit-installment"
            value={TransactioCategory.クレジットカード分割}
            label="クレジットカード分割"
            checked={transactionCategory?.includes(TransactioCategory.クレジットカード分割)}
            onChange={onChange}
        />
        <CheckboxItem
            id="transaction-category-credit-revolving"
            value={TransactioCategory.クレジットカードリボ}
            label="クレジットカードリボ"
            checked={transactionCategory?.includes(TransactioCategory.クレジットカードリボ)}
            onChange={onChange}
        />

        {/* Payment Types Items */}
        <CheckboxItem
            id="payment-type-unionpay"
            value={TransactionType.銀聯}
            label="銀聯"
            checked={paymentTypes?.includes(TransactionType.銀聯)}
            onChange={onChange}
        />
        <CheckboxItem
            id="payment-type-transport-ic"
            value={TransactionType.交通系電子マネー}
            label="交通系電子マネー"
            checked={paymentTypes?.includes(TransactionType.交通系電子マネー)}
            onChange={onChange}
        />
        <CheckboxItem
            id="payment-type-waon"
            value={TransactionType.WAON}
            label="WAON"
            checked={paymentTypes?.includes(TransactionType.WAON)}
            onChange={onChange}
        />
        <CheckboxItem
            id="payment-type-nanaco"
            value={TransactionType.nanaco}
            label="nanaco"
            checked={paymentTypes?.includes(TransactionType.nanaco)}
            onChange={onChange}
        />
        <CheckboxItem
            id="payment-type-edy"
            value={TransactionType.Edy}
            label="Edy"
            checked={paymentTypes?.includes(TransactionType.Edy)}
            onChange={onChange}
        />
        <CheckboxItem
            id="payment-type-id"
            value={TransactionType.iD}
            label="iD"
            checked={paymentTypes?.includes(TransactionType.iD)}
            onChange={onChange}
        />
        <CheckboxItem
            id="payment-type-quicpay"
            value={TransactionType.QUICPay}
            label="QUICPay"
            checked={paymentTypes?.includes(TransactionType.QUICPay)}
            onChange={onChange}
        />
        <CheckboxItem
            id="payment-type-qr"
            value={TransactionType.QRコード}
            label="QRコード"
            checked={paymentTypes?.includes(TransactionType.QRコード)}
            onChange={onChange}
        />
    </div>
);
