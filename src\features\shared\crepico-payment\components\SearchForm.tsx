import React, { ChangeEvent } from 'react';
import { DateTimePickerCustom } from './DateTimePickerCustom';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import {
    PaymentStatusGroup,
    CombinedTransactionGroup
} from './CheckboxGroup';
import { Input } from '@/components/ui/input';

interface ValidateError {
    paymentTime: string;
    saleAmount: string;
}

// Union type to support both string[] (Store) and TerminalNoItem[] (AdminStore)
type TerminalNoItem = {
    label: string;
    value: string;
};

type TerminalNos = string[] | TerminalNoItem[];

interface SearchFormProps {
    agxStoreName?: string;
    agxNewTerminalNos: TerminalNos;
    terminalIdentification: string;
    paymentStatus: string[];
    paymentTypes: string[];
    transactionCategory: string[];
    paymentTimeFrom: string;
    paymentTimeTo: string;
    saleAmountFrom: string;
    saleAmountTo: string;
    error: ValidateError;
    TransactionCode: any;
    TransactioCategory: any;
    TransactionType: any;
    onTerminalIdentificationChange: (value: string) => void;
    onPaymentStatusChange: (e: ChangeEvent<HTMLInputElement>) => void;
    onTransactionCategoryChange: (e: ChangeEvent<HTMLInputElement>) => void;
    onPaymentTypesChange: (e: ChangeEvent<HTMLInputElement>) => void;
    onPaymentTimeFromChange: (date: Date | null) => void;
    onPaymentTimeToChange: (date: Date | null) => void;
    onSaleAmountFromChange: (e: ChangeEvent<HTMLInputElement>) => void;
    onSaleAmountToChange: (e: ChangeEvent<HTMLInputElement>) => void;
    onSubmitSearch: () => void;
    isAdminStore: boolean; // Use isAdminStore to determine if "返品" checkbox should be hidden
}

export const SearchForm: React.FC<SearchFormProps> = ({
    agxStoreName,
    agxNewTerminalNos,
    terminalIdentification,
    paymentStatus,
    paymentTypes,
    transactionCategory,
    paymentTimeFrom,
    paymentTimeTo,
    saleAmountFrom,
    saleAmountTo,
    error,
    TransactionCode,
    TransactioCategory,
    TransactionType,
    onTerminalIdentificationChange,
    onPaymentStatusChange,
    onTransactionCategoryChange,
    onPaymentTypesChange,
    onPaymentTimeFromChange,
    onPaymentTimeToChange,
    onSaleAmountFromChange,
    onSaleAmountToChange,
    onSubmitSearch,
    isAdminStore
}) => {
    // Helper function to check if terminal data is TerminalNoItem[]
    const isTerminalNoItemArray = (terminals: TerminalNos): terminals is TerminalNoItem[] => {
        return terminals.length > 0 && typeof terminals[0] === 'object' && 'label' in terminals[0];
    };

    return (
        <div className="md:mt-6 mb-6">
            <div className="grid grid-cols-12 gap-4">
                {/* Store Name */}
                <div className="col-span-4 md:col-span-2">
                    <p className="font-semibold">加盟店名</p>
                </div>
                <div className="col-span-8 md:col-span-9">
                    <p>{agxStoreName}</p>
                </div>

                {/* Terminal Identification */}
                <div className="col-span-12 md:col-span-2 md:mt-10">
                    <p className='font-semibold'>端末識別番号 <span className='lg:ml-10'>(TID)</span></p>
                </div>
                <div className="col-span-12 md:col-span-9 md:mt-10">
                    <div className="flex">
                        <Select value={terminalIdentification || null} onValueChange={onTerminalIdentificationChange}>
                            <SelectTrigger className="sm:w-1/2 lg:w-1/3 2xl:w-1/4 text-lg border border-[#6F6F6E] rounded">
                                <SelectValue className='text-lg' placeholder={agxNewTerminalNos?.length === 0 ? "この機能は利用できません" : "選択してください"} />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem className='text-lg' value={null}>
                                    {agxNewTerminalNos?.length === 0 ? "この機能は利用できません" : "選択してください"}
                                </SelectItem>
                                {isTerminalNoItemArray(agxNewTerminalNos)
                                    ? agxNewTerminalNos?.map((terminalNo, index) => (
                                        terminalNo && (
                                            <SelectItem className='text-lg' key={index} value={terminalNo?.value}>
                                                {terminalNo?.label}
                                            </SelectItem>
                                        )
                                    ))
                                    : agxNewTerminalNos?.map((terminalNo) => (
                                        terminalNo && (
                                            <SelectItem className='text-lg' key={terminalNo} value={terminalNo}>
                                                {terminalNo}
                                            </SelectItem>
                                        )
                                    ))
                                }
                            </SelectContent>
                        </Select>
                    </div>
                </div>

                {/* Payment Status */}
                <div className="col-span-12 md:col-span-2 md:mt-10">
                    <p className='font-semibold'>決済状況</p>
                </div>
                <div className="col-span-12 md:col-span-9 md:mt-10">
                    <PaymentStatusGroup
                        paymentStatus={paymentStatus}
                        onChange={onPaymentStatusChange}
                        TransactionCode={TransactionCode}
                        hideReturn={isAdminStore}
                    />
                    <span className='text-base'>※当日中の売上を取消した場合は「取消」、前日以前の売上を取消した場合は「返品」</span>
                </div>

                {/* Combined Transaction Category and Payment Types */}
                <div className="col-span-12 md:col-span-2 md:mt-10">
                    <p className='font-semibold'>決済種別</p>
                </div>
                <div className="col-span-12 md:col-span-9 md:mt-10">
                    <CombinedTransactionGroup
                        transactionCategory={transactionCategory}
                        paymentTypes={paymentTypes}
                        onChange={(e) => {
                            // Check if the value belongs to transaction category or payment type
                            const isTransactionCategory = Object.values(TransactioCategory).includes(e.target.value);
                            if (isTransactionCategory) {
                                onTransactionCategoryChange(e);
                            } else {
                                onPaymentTypesChange(e);
                            }
                        }}
                        TransactioCategory={TransactioCategory}
                        TransactionType={TransactionType}
                    />
                </div>

                {/* Payment Time */}
                <div className="col-span-12 md:col-span-2 md:mt-10">
                    <p className='font-semibold text-[#6F6F6E]'>決済日時</p>
                </div>
                <div className="col-span-12 md:col-span-9 md:mt-10">
                    <div className="flex items-center gap-4 md:flex-row flex-col">
                        <div>
                            <DateTimePickerCustom
                                value={paymentTimeFrom}
                                onChange={onPaymentTimeFromChange}
                                placeholder="YYYY/MM/DD HH:mm"
                                className="w-[260px] px-2 rounded text-2xl h-[40px]"
                            />
                        </div>
                        <span className="text-[#6F6F6E] text-2xl scale-150 mx-4">~</span>
                        <div>
                            <DateTimePickerCustom
                                value={paymentTimeTo}
                                onChange={onPaymentTimeToChange}
                                placeholder="YYYY/MM/DD HH:mm"
                                className="w-[260px] px-2 rounded text-2xl h-[40px]"
                            />
                        </div>
                        {error.paymentTime && (
                            <span className='ml-2 text-red-500'>
                                {error.paymentTime}
                            </span>
                        )}
                    </div>
                </div>

                {/* Sale Amount */}
                <div className="col-span-12 md:col-span-2 md:mt-10">
                    <p className='font-semibold text-[#6F6F6E]'>売上金額</p>
                </div>
                <div className="col-span-12 md:col-span-9 md:mt-10">
                    <div className="flex items-center gap-4 md:flex-row flex-col">
                        <Input
                            type="number"
                            min={0}
                            value={saleAmountFrom}
                            onChange={onSaleAmountFromChange}
                            className="w-[260px] px-2 border border-[#6F6F6E] rounded text-2xl h-[40px]"
                        />
                        <span className="text-[#6F6F6E] text-2xl scale-150 mx-4">~</span>
                        <Input
                            type="number"
                            min={0}
                            value={saleAmountTo}
                            onChange={onSaleAmountToChange}
                            className="w-[260px] px-2 border border-[#6F6F6E] rounded text-2xl h-[40px]"
                        />
                        {error.saleAmount && (
                            <span className='ml-2 text-red-500'>
                                {error.saleAmount}
                            </span>
                        )}
                    </div>
                </div>
            </div>

            {/* Search Button */}
            <div className='flex justify-center md:justify-start'>
                <Button
                    type="button"
                    className='w-[190px] bg-[#37ae9c] hover:bg-[#3da192] text-white py-6 font-semibold rounded-lg transition-colors mt-10 text-lg'
                    title='検索'
                    onClick={onSubmitSearch}
                >
                    検索
                </Button>
            </div>
        </div>
    );
};
