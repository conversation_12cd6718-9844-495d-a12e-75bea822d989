import { useState, useCallback, useMemo, useRef, ChangeEvent } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuthStore } from '@/store';
import { crepicoPaymentService } from '@/features/store/crepico-payment/services/crepicoPaymentService';
import { editService } from '@/features/store/crepico-payment/services/editService';
import { getNewTerminalNoService } from '@/features/adminStore/crepico-payment/services/getNewTerminalNoService';
import {
    ValidateError,
    SortDirection,
    PaymentsResponse,
    INIT_VALIDATE_ERROR,
    INIT_SORT_DIRECTION,
    PAGE_SIZE,
    CSV_HEADERS,
    CrepicoPaymentConfig,
    PaymentData
} from '../types';
import {
    mapTransactionCodePaymentData,
    mapTransactionTypeCredit,
    mapTransactionTypeOther
} from '@/constants/common.constant';
import { DateUtils} from '@/utils/dateUtils';

interface UseCrepicoPaymentProps {
    config: CrepicoPaymentConfig;
}

export const useCrepicoPayment = ({ config }: UseCrepicoPaymentProps) => {
    // Auth data
    const { user } = useAuthStore();
    const agxMerchantNo = user?.agxMerchantNo;
    const agxNewMerchantNo = user?.agxNewMerchantNo;
    const agxStoreName = user?.agxStoreName;

    // State management
    const [isInitialLoad, setIsInitialLoad] = useState<boolean>(true);
    const [page, setPage] = useState<number>(0);
    const [paymentStatus, setPaymentStatus] = useState<string[]>([]);
    const [paymentTypes, setPaymentTypes] = useState<string[]>([]);
    const [transactionCategory, setTransactionCategory] = useState<string[]>([]);
    const [paymentTimeFrom, setPaymentTimeFrom] = useState<string>('');
    const [paymentTimeTo, setPaymentTimeTo] = useState<string>('');
    const [saleAmountFrom, setSaleAmountFrom] = useState<string>('');
    const [saleAmountTo, setSaleAmountTo] = useState<string>('');
    const [terminalIdentification, setTerminalIdentification] = useState<string>(null);
    const [payments, setPayments] = useState<PaymentsResponse | null>(null);
    const [error, setError] = useState<ValidateError>(INIT_VALIDATE_ERROR);
    const [sortValue, setSortValue] = useState<string>('');
    const [sortDirection, setSortDirection] = useState<boolean>(false);
    const [sortDirectionByValue, setSortDirectionByValue] = useState<SortDirection>(INIT_SORT_DIRECTION);

    const [csvData, setCsvData] = useState<any[]>([]);
    const csvLinkRef = useRef<any>(null);

    // Merchant number logic - different for Store vs AdminStore
    const merchantNo = useMemo(() => {
        if (config.isAdminStore) {
            return agxMerchantNo;
        }
        return user?.memberType ? agxMerchantNo : agxNewMerchantNo;
    }, [config.isAdminStore, user?.memberType, agxMerchantNo, agxNewMerchantNo]);

    // Fetch terminal numbers using useQuery - different service for Store vs AdminStore
    const {
        data: terminalNosData,
        isLoading: terminalNosLoading,
        error: terminalNosError,
        refetch: refetchTerminalNos
    } = useQuery({
        queryKey: ['terminal-nos', merchantNo, config.isAdminStore],
        queryFn: async () => {
            if (config.isAdminStore) {
                // AdminStore version
                const response = await getNewTerminalNoService.getData(merchantNo);
                return response?.data?.newTerminalNo || [];
            } else {
                // Store version
                const data = [];
                const merchant = await editService.getElement(merchantNo);
                const merchantData = merchant?.data;

                for (let i = 1; i <= 10; i++) {
                    const terminalNo = merchantData?.[`agxNewTerminalNo${i}`];
                    if (terminalNo) {
                        data.push(terminalNo);
                    }
                }
                return data;
            }
        },
        enabled: !!merchantNo,
        staleTime: 5 * 60 * 1000, // 5 minutes
    });

    // Terminal numbers data
    const agxNewTerminalNos = terminalNosData || [];

    // Event handlers
    const handlePaymentStatus = useCallback((e: ChangeEvent<HTMLInputElement>) => {
        const { value, checked } = e.target;
        setPaymentStatus(prev =>
            checked
                ? [...prev, value]
                : prev.filter(status => status !== value)
        );
    }, []);

    const handleTransactionCategory = useCallback((e: ChangeEvent<HTMLInputElement>) => {
        const { value, checked } = e.target;
        setTransactionCategory(prev =>
            checked
                ? [...prev, value]
                : prev.filter(tc => tc !== value)
        );
    }, []);

    const handlePaymentTypes = useCallback((e: ChangeEvent<HTMLInputElement>) => {
        const { value, checked } = e.target;
        setPaymentTypes(prev =>
            checked
                ? [...prev, value]
                : prev.filter(pt => pt !== value)
        );
    }, []);

    const handleTerminalIdentification = useCallback((value: string) => {
        setTerminalIdentification(value || null);
    }, []);

    const validate = useCallback((timeFrom: string, timeTo: string, amountFrom: string, amountTo: string): boolean => {
        const er = { ...INIT_VALIDATE_ERROR };

        if (timeFrom && timeTo && timeFrom > timeTo) {
            er.paymentTime = '開始日は終了日以降である必要があります';
        }

        const amountFromNum = parseFloat(amountFrom);
        const amountToNum = parseFloat(amountTo);

        if (amountFromNum < 0 || amountToNum < 0) {
            er.saleAmount = '0以上の値を入力してください';
        } else if (amountFrom && amountTo && amountFromNum > amountToNum) {
            er.saleAmount = '開始番号は終了番号以下である必要があります';
        }

        setError(er);
        return er.paymentTime.length === 0 && er.saleAmount.length === 0;
    }, []);

    // Helper function to format date for API
    const formatDateForAPI = (date: Date): string => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
    };

    const handlePaymentTimeFrom = useCallback((date: Date | null) => {
        const timeFrom = date ? formatDateForAPI(date) : '';
        validate(timeFrom, paymentTimeTo, saleAmountFrom, saleAmountTo);
        setPaymentTimeFrom(timeFrom);
    }, [paymentTimeTo, saleAmountFrom, saleAmountTo, validate]);

    const handlePaymentTimeTo = useCallback((date: Date | null) => {
        const timeTo = date ? formatDateForAPI(date) : '';
        validate(paymentTimeFrom, timeTo, saleAmountFrom, saleAmountTo);
        setPaymentTimeTo(timeTo);
    }, [paymentTimeFrom, saleAmountFrom, saleAmountTo, validate]);

    const handleSaleAmountFrom = useCallback((e: ChangeEvent<HTMLInputElement>) => {
        const amountFrom = e.target.value || '';
        validate(paymentTimeFrom, paymentTimeTo, amountFrom, saleAmountTo);
        setSaleAmountFrom(amountFrom);
    }, [paymentTimeFrom, paymentTimeTo, saleAmountTo, validate]);

    const handleSaleAmountTo = useCallback((e: ChangeEvent<HTMLInputElement>) => {
        const amountTo = e.target.value || '';
        validate(paymentTimeFrom, paymentTimeTo, saleAmountFrom, amountTo);
        setSaleAmountTo(amountTo);
    }, [paymentTimeFrom, paymentTimeTo, saleAmountFrom, validate]);

    // Search query - đơn giản hóa
    const searchQuery = useQuery({
        queryKey: ['crepico-payments-search', merchantNo, terminalIdentification, paymentStatus, paymentTypes, transactionCategory, paymentTimeFrom, paymentTimeTo, saleAmountFrom, saleAmountTo, page, sortValue, sortDirection],
        queryFn: async () => {
            const { data, totalYen, totalItem } = await crepicoPaymentService.searchCrepicoPaymentData(
                merchantNo,
                terminalIdentification,
                paymentStatus.join(','),
                paymentTypes.join(','),
                transactionCategory.join(','),
                paymentTimeFrom,
                paymentTimeTo,
                saleAmountFrom,
                saleAmountTo,
                page,
                PAGE_SIZE,
                sortValue,
                sortDirection.toString()
            );
            setPayments({ data, totalYen, totalItem });
            return { data, totalYen, totalItem };
        },
        enabled: false,
    });

    // Search function - giữ nguyên cách dùng như cũ
    const handleSubmitSearch = useCallback(() => {
        searchQuery.refetch();
    }, [searchQuery]);

    const handleSort = useCallback((key: keyof SortDirection) => {
        setSortValue(key);
        const newDirection = sortDirectionByValue[key] === null ? true : !sortDirectionByValue[key];
        setSortDirection(newDirection);
        setSortDirectionByValue({ ...INIT_SORT_DIRECTION, [key]: newDirection });
    }, [sortDirectionByValue]);

    // Export query
    const exportQuery = useQuery({
        queryKey: ['crepico-payments-export', merchantNo, terminalIdentification, paymentStatus, paymentTypes, transactionCategory, paymentTimeFrom, paymentTimeTo, saleAmountFrom, saleAmountTo, sortValue, sortDirection, payments?.data?.totalElements],
        queryFn: async () => {
            const exportPageSize = payments?.data?.totalElements;

            const { data } = await crepicoPaymentService.searchCrepicoPaymentData(
                merchantNo,
                terminalIdentification,
                paymentStatus.join(','),
                paymentTypes.join(','),
                transactionCategory.join(','),
                paymentTimeFrom,
                paymentTimeTo,
                saleAmountFrom,
                saleAmountTo,
                0,
                exportPageSize,
                sortValue,
                sortDirection.toString()
            );

            const csvRows = data.data.map((p: PaymentData) => ({
                ...p,
                transactionCode: mapTransactionCodePaymentData.get(p.transactionCode),
                serviceIdentificationFlag: p.serviceIdentificationFlag == '1'
                    ? mapTransactionTypeCredit.get(p.transactionCategory)
                    : mapTransactionTypeOther.get(p.serviceIdentificationFlag)
            }));

            // Set CSV data và trigger download
            setCsvData(csvRows || []);
            setTimeout(() => {
                if (csvLinkRef.current) {
                    csvLinkRef.current.link.click();
                }
            }, 100);

            return csvRows || [];
        },
        enabled: false,
        staleTime: 0, // Luôn fetch data mới cho export
    });

    // Export function
    const handleExportData = useCallback(() => {
        exportQuery.refetch();
    }, [exportQuery]);

    // CSV export configuration
    const csvExport = useMemo(() => ({
        data: csvData,
        headers: CSV_HEADERS,
        filename: `crepico_payment_${DateUtils.format(new Date(), 'YYYY年MM月DD日')}.csv`,
        separator: ',',
        enclosingCharacter: ''
    }), [csvData]);

    return {
        // State
        isInitialLoad,
        setIsInitialLoad,
        page,
        setPage,
        paymentStatus,
        paymentTypes,
        transactionCategory,
        paymentTimeFrom,
        paymentTimeTo,
        saleAmountFrom,
        saleAmountTo,
        terminalIdentification,
        payments,
        error,
        sortValue,
        sortDirection,
        sortDirectionByValue,
        agxNewTerminalNos,
        csvData,
        csvLinkRef,

        // Computed values
        merchantNo,
        agxStoreName,
        csvExport,

        // React Query states
        terminalNosLoading,
        terminalNosError,
        searchLoading: searchQuery.isFetching,
        searchError: searchQuery.error,
        exportLoading: exportQuery.isFetching,
        exportError: exportQuery.error,

        // Functions
        refetchTerminalNos,
        handlePaymentStatus,
        handleTransactionCategory,
        handlePaymentTypes,
        handleTerminalIdentification,
        handlePaymentTimeFrom,
        handlePaymentTimeTo,
        handleSaleAmountFrom,
        handleSaleAmountTo,
        handleSubmitSearch,
        handleSort,
        handleExportData,
        validate
    };
};
