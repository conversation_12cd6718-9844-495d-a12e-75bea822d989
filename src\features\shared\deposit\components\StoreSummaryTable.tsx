import React from 'react';
import { Link } from 'react-router-dom';
import { formatNumber } from '@/utils/dateUtils';
import { STORE } from '@/types/globalType';

interface MerchantPayment {
  agxNumberOfSales: number;
  agxSalesAmount: number;
  agxTotalFee: number;
  agxSettlementCompanyTax: number;
  agxInHouseTax: number;
  agxPaymentAmount: number;
  agxInvoiceFlg: number;
  agxBankName: string;
  agxBranchName: string;
  agxAcccountType: string;
  agxAccountNo: string;
  agxAccountHolder: string;
}

interface StoreSummaryTableProps {
  merchantPayments: MerchantPayment[];
  total?: number;
  transferDate: string;
  type: string;
  isNewFormat: boolean;
  showMonthlyFee: boolean;
}

export const StoreSummaryTable: React.FC<StoreSummaryTableProps> = ({
  merchantPayments,
  total,
  transferDate,
  type,
  isNewFormat,
  showMonthlyFee
}) => {
  const merchantPayment = merchantPayments?.[0];

  if (!merchantPayment) return null;

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 text-[#6F6F6E] mt-4">
      <div className="lg:col-span-2 border-b border-[#6F6F6E] px-4 pb-4">
        <div className="mb-4">
          <div className="text-2xl text-[#6F6F6E]">
            サマリー ｜
            <Link
              to={`/store/deposit/${type}/detail/${transferDate}`}
              className="text-[#1D9987] hover:text-[#1D9987]/80"
            >
              詳細データを確認する
            </Link>
          </div>
        </div>

        <div className="overflow-x-auto text-lg px-8">
          <div className='flex flex-col'>
            {/* Header row */}
            <div className='flex flex-row gap-4 xl:gap-10'>
              <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                売上件数
              </div>
              <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                売上金額
              </div>
              <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                手数料額
              </div>
              <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                （内消費税）
              </div>
              {showMonthlyFee && (
                <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                  月額費用
                </div>
              )}
              <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                {type === STORE.PAYGATE ? '振込金額' : '振込額'}
              </div>
            </div>
            {/* Data row */}
            <div className='flex flex-row gap-4 xl:gap-8'>
              <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                {merchantPayment.agxNumberOfSales !== null && merchantPayment.agxNumberOfSales !== undefined ? `${formatNumber(merchantPayment.agxNumberOfSales)}件` : ''}
              </div>
              <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                {merchantPayment.agxSalesAmount !== null && merchantPayment.agxSalesAmount !== undefined ? `${formatNumber(merchantPayment.agxSalesAmount)}円` : ''}
              </div>
              <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                {merchantPayment.agxTotalFee !== null && merchantPayment.agxTotalFee !== undefined ? `${formatNumber(merchantPayment.agxTotalFee)}円` : ''}
              </div>
              <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                {`(${formatNumber((merchantPayment.agxSettlementCompanyTax || 0) + (merchantPayment.agxInHouseTax || 0))})円`}
              </div>
              {showMonthlyFee && (
                <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                  {total !== null && total !== undefined ? `${formatNumber(total)}円` : ''}
                </div>
              )}
              <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                {merchantPayment.agxPaymentAmount !== null && merchantPayment.agxPaymentAmount !== undefined ? `${formatNumber(merchantPayment.agxPaymentAmount)}円` : ''}
              </div>
            </div>
          </div>
        </div>

        <div className="mt-4 text-[#6F6F6E] text-lg px-8">
          <div>※毎月初の振込は、売上金額より月額費用を差し引いております。売上金額が月額費用未満であった場合、</div>
          <div>&nbsp;&nbsp;&nbsp;&nbsp;別途月額費用を弊社宛てにお支払いいただく必要があります。</div>
        </div>
      </div>

      {/* Bank Info */}
      <div className='px-8 pb-4'>
        {isNewFormat && (
          <div className="mb-4">
            <div className="text-[#6F6F6E] text-lg">適格事業者登録番号</div>
            <div className="text-[#6F6F6E] text-lg">(T6120001228218)</div>
          </div>
        )}

        <div className="mb-2 text-[#6F6F6E] text-lg">振込先金融機関</div>
        <div className="text-lg space-y-1">
          <div className="flex">
            <span className="w-24">金融機関名</span>
            <span className="mr-2">:</span>
            <span>{merchantPayment.agxBankName || ''}</span>
          </div>
          <div className="flex">
            <span className="w-24">支店名</span>
            <span className="mr-2">:</span>
            <span>{merchantPayment.agxBranchName || ''}</span>
          </div>
          <div className="flex">
            <span className="w-24">口座種別</span>
            <span className="mr-2">:</span>
            <span>{merchantPayment.agxAcccountType || ''}</span>
          </div>
          <div className="flex">
            <span className="w-24">口座番号</span>
            <span className="mr-2">:</span>
            <span>{merchantPayment.agxAccountNo || ''}</span>
          </div>
          <div className="flex">
            <span className="w-24">名義人</span>
            <span className="mr-2">:</span>
            <span>{merchantPayment.agxAccountHolder || ''}</span>
          </div>
        </div>
      </div>
    </div>
  );
};
