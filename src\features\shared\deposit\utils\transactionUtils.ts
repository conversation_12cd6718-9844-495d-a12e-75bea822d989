// Transaction type classification helpers
export const isCredictCardTransaction = (transactionType: number): boolean =>
  transactionType <= 283260004 || (transactionType >= 283260018 && transactionType <= 283260023);

export const isElectronicMoneyTransaction = (transactionType: number): boolean =>
  transactionType === 283260024 || (transactionType >= 283260005 && transactionType <= 283260009);

export const isQRCodeTransaction = (transactionType: number): boolean =>
  transactionType >= 283260010 && transactionType <= 283260017;
