import React, { useState, useMemo } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuthStore } from '@/features/auth/slices/authStore';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { Table, TableBody, TableCell, TableRow, TableHead, TableHeader } from '@/components/ui/table';
import { useDepositPage } from '@/features/store/deposit/hooks/useDepositPage';
import { Doughnut } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';
import { formatNumber, formatDateJapan } from '@/utils/dateUtils';
import { mapTransactionType, mapTransactionImage } from '@/constants/common.constant';
import { ExportButtons } from './ExportButtons';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { STORE } from '@/types/globalType';
import { TaxSummaryTable } from '@/features/shared/components/TaxSummaryTable';
// Register Chart.js components
ChartJS.register(ArcElement, Tooltip, Legend);

// Chart configuration constants
const CHART_COLORS = {
  backgroundColor: [
    'rgba(255, 99, 132, 0.6)',
    'rgba(255, 206, 86, 0.6)',
    'rgba(54, 162, 235, 0.6)',
  ],
  borderColor: [
    'rgba(255, 99, 132, 0.6)',
    'rgba(255, 206, 86, 0.6)',
    'rgba(54, 162, 235, 0.6)',
  ]
};

const CHART_OPTIONS = {
  responsive: true,
  maintainAspectRatio: false,
  layout: {
    padding: {
      top: 20,
      left: 20,
      right: 20
    }
  },
  plugins: {
    legend: {
      display: true,
      position: 'top' as const,
      labels: {
        fontColor: '#000',
        fontSize: 10,
        boxWidth: 10,
      },
      fit: 10
    },
    tooltips: {
      enabled: true
    },
  },
  cutout: '50%', // This makes it a doughnut chart
};

// Transaction type classification helpers
const isCredictCardTransaction = (transactionType: number) =>
  transactionType <= ********* || (transactionType >= 283260018 && transactionType <= 283260023);

const isElectronicMoneyTransaction = (transactionType: number) =>
  transactionType === 283260024 || (transactionType >= 283260005 && transactionType <= 283260009);

const isQRCodeTransaction = (transactionType: number) =>
  transactionType >= 283260010 && transactionType <= 283260017;

interface DepositPageProps {
  agxMerchantNo: string;
  type: string;
}

export const DepositPage: React.FC<DepositPageProps> = ({ agxMerchantNo, type }) => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const agxStoreName = user?.agxStoreName || '';

  const {
    dates,
    depositData,
    transferDate,
    datesLoading,
    depositLoading,
    error,
    dlEnable,
    setTransferDate
  } = useDepositPage(agxMerchantNo);

  const [typeChart, setTypeChart] = useState("/store/deposit/" + type);

  // 11/06の振り込み分から新しいフォーマット
  const switchLayoutDate = "2023-11-05";

  // Calculate chart data using useMemo for better performance
  const chartData = useMemo(() => {
    if (!depositData?.paymentBreakdowns || !depositData?.merchantPayments?.length) {
      return {
        labelsNumberOfSales: [],
        labelsSalesAmount: [],
        percentageNumberOfSales: [],
        percentageSalesAmount: []
      };
    }

    const totalNumberOfSales = depositData.merchantPayments[0].agxNumberOfSales;
    const totalSalesAmount = depositData.merchantPayments[0].agxSalesAmount;

    let creditCardSales = 0;
    let creditCardAmount = 0;
    let electronicMoneySales = 0;
    let electronicMoneyAmount = 0;
    let qrCodeSales = 0;
    let qrCodeAmount = 0;

    depositData.paymentBreakdowns.forEach(element => {
      const transactionType = element.agxTransactionType;

      if (isCredictCardTransaction(transactionType)) {
        creditCardSales += element.agxNumberOfSales;
        creditCardAmount += element.agxSalesAmount;
      } else if (isElectronicMoneyTransaction(transactionType)) {
        electronicMoneySales += element.agxNumberOfSales;
        electronicMoneyAmount += element.agxSalesAmount;
      } else if (isQRCodeTransaction(transactionType)) {
        qrCodeSales += element.agxNumberOfSales;
        qrCodeAmount += element.agxSalesAmount;
      }
    });

    const creditCardSalesPercent = ((creditCardSales / totalNumberOfSales) * 100).toFixed(1);
    const electronicMoneySalesPercent = ((electronicMoneySales / totalNumberOfSales) * 100).toFixed(1);
    const qrCodeSalesPercent = ((qrCodeSales / totalNumberOfSales) * 100).toFixed(1);

    const creditCardAmountPercent = ((creditCardAmount / totalSalesAmount) * 100).toFixed(1);
    const electronicMoneyAmountPercent = ((electronicMoneyAmount / totalSalesAmount) * 100).toFixed(1);
    const qrCodeAmountPercent = ((qrCodeAmount / totalSalesAmount) * 100).toFixed(1);

    return {
      labelsNumberOfSales: [
        `クレジットカード決済[${creditCardSalesPercent}%]`,
        `電子マネー決済[${electronicMoneySalesPercent}%]`,
        `QRコード決済[${qrCodeSalesPercent}%]`
      ],
      labelsSalesAmount: [
        `クレジットカード決済[${creditCardAmountPercent}%]`,
        `電子マネー決済[${electronicMoneyAmountPercent}%]`,
        `QRコード決済[${qrCodeAmountPercent}%]`
      ],
      percentageNumberOfSales: [creditCardSalesPercent, electronicMoneySalesPercent, qrCodeSalesPercent],
      percentageSalesAmount: [creditCardAmountPercent, electronicMoneyAmountPercent, qrCodeAmountPercent]
    };
  }, [depositData]);

  const handleChangeTypeChart = (value: string) => {
    setTypeChart(value);
    navigate(value);
  };

  const handleChangeDate = (value: string) => {
    setTransferDate(value);
  };

  // Computed values for better readability
  const hasDepositData = !!depositData;
  const hasPaymentData = hasDepositData && depositData.merchantPayments?.length > 0;
  const isNewFormat = transferDate > switchLayoutDate;
  const showMonthlyFee = hasPaymentData && depositData.merchantPayments[0].agxInvoiceFlg !== 283260002;
  const showInvoiceFlag = hasPaymentData && depositData.merchantPayments[0].agxInvoiceFlg === 283260000;

  // Helper function to create chart data for Doughnut chart
  const createChartData = (labels: string[], percentages: string[]) => ({
    labels: labels,
    datasets: [
      {
        data: percentages.map(p => parseFloat(p)),
        backgroundColor: CHART_COLORS.backgroundColor,
        borderColor: CHART_COLORS.borderColor,
        borderWidth: 1,
        radius: 150,
      },
    ],
  });
  const createPlugins = (total: number) => [{
    id: 'centerText',
    beforeDraw: function (chart: any) {
      const width = chart.width;
      const height = chart.height;
      const ctx = chart.ctx;
      ctx.restore();
      const fontSize = (height / 220).toFixed(3);
      ctx.font = fontSize + "em sans-serif";
      ctx.textBaseline = "top";
      const text = formatNumber(total);
      const textX = Math.round((width - ctx.measureText(text).width) / 2);
      const textY = (height + 35) / 2;
      ctx.fillText(text, textX, textY);
      ctx.save();
    }
  }];

  if (datesLoading || depositLoading) {
    return (
      <LoadingSpinner />
    )
  }

  if (!hasDepositData) {
    return (
      <div className="flex justify-center items-center py-12">
        <h2 className="text-xl text-gray-600">振込データがありません。</h2>
      </div>
    )
  }

  return (
    <div className="p-2 mb-16">
      {/* Page Header */}
      <div className="px-4 pb-4 border-b border-[#6F6F6E] mt-4">
        <div className="flex items-center gap-12">
          <Select value={typeChart} onValueChange={handleChangeTypeChart}>
            <div className="relative">
              <SelectTrigger className="w-[18rem] text-[#6F6F6E] text-xl border-gray-300 border-2 shadow-md [&>span:first-child]:text-center [&>span:first-child]:w-full [&>svg]:hidden">
                <SelectValue />
              </SelectTrigger>
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                <div className="w-0 h-0 border-l-[6px] border-r-[6px] border-t-[8px] border-l-transparent border-r-transparent border-t-black opacity-50"></div>
              </div>
            </div>
            <SelectContent>
              <SelectItem className="text-[#6F6F6E] text-base" value={`/store/deposit/${type}`}>振込一覧</SelectItem>
              <SelectItem className='text-[#6F6F6E] text-base' value={`/store/summary/${type}`}>売上金額・件数推移（振込日別）</SelectItem>
              <SelectItem className='text-[#6F6F6E] text-base' value={`/store/summary-monthly/${type}`}>売上金額・件数推移（振込月別）</SelectItem>
            </SelectContent>
          </Select>

          {dates.length > 0 && (
            <>
              <span className="text-2xl text-[#6F6F6E]">振込日</span>
              <Select value={transferDate} onValueChange={handleChangeDate}>
                <div className="relative">
                  <SelectTrigger className="w-[18rem] text-[#6F6F6E] text-xl border-gray-300 border-2 shadow-md [&>span:first-child]:text-center [&>span:first-child]:w-full [&>svg]:hidden">
                    <SelectValue />
                  </SelectTrigger>
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                    <div className="w-0 h-0 border-l-[6px] border-r-[6px] border-t-[8px] border-l-transparent border-r-transparent border-t-black opacity-50"></div>
                  </div>
                </div>
                <SelectContent>
                  {dates.map((date, index) => (
                    <SelectItem className="text-[#6F6F6E] text-base" key={index} value={date}>
                      {formatDateJapan(date)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </>
          )}

          <ExportButtons
            depositData={depositData}
            transferDate={transferDate}
            agxMerchantNo={agxMerchantNo}
            agxStoreName={agxStoreName}
            type={type}
            dlEnable={dlEnable}
          />

          <span className="text-2xl ">
            利用期間｜20xx年mm月dd日〜20xx年mm月dd日
          </span>
        </div>
      </div>

      {error && (
        <Alert className="mb-6" variant="destructive">
          <AlertDescription>{error.message || '振込データがありません。'}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-6 text-[#6F6F6E]">
        {depositData && (
          <>
            {/* Store Info and Summary Table */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 text-[#6F6F6E] mt-4 ">
              <div className="lg:col-span-2 border-b border-[#6F6F6E] px-4 pb-4">
                <div className="mb-4">
                  <div className="text-2xl text-[#6F6F6E]">
                    サマリー ｜
                    <Link
                      to={`/store/deposit/${type}/detail/${transferDate}`}
                      className="text-[#1D9987] hover:text-[#1D9987]/80"
                    >
                      詳細データを確認する
                    </Link>
                  </div>
                </div>

                <div className="overflow-x-auto text-lg px-8">
                  <div className='flex flex-col'>
                    {/* Header row */}
                    <div className='flex flex-row gap-4 xl:gap-10'>
                      <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                        売上件数
                      </div>
                      <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                        売上金額
                      </div>
                      <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                        手数料額
                      </div>
                      <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                        （内消費税）
                      </div>
                      {showMonthlyFee && (
                        <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                          月額費用
                        </div>
                      )}
                      <div className="py-3 px-0 text-center bg-white border-b border-[#6F6F6E] flex-[1] xl:px-2">
                        {type === STORE.PAYGATE ? '振込金額' : '振込額'}
                      </div>
                    </div>
                    {/* Data row */}
                    <div className='flex flex-row gap-4 xl:gap-8'>
                      <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                        {depositData.merchantPayments?.[0] && depositData.merchantPayments[0].agxNumberOfSales !== null && depositData.merchantPayments[0].agxNumberOfSales !== undefined ? `${formatNumber(depositData.merchantPayments[0].agxNumberOfSales)}件` : ''}
                      </div>
                      <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                        {depositData.merchantPayments?.[0] && depositData.merchantPayments[0].agxSalesAmount !== null && depositData.merchantPayments[0].agxSalesAmount !== undefined ? `${formatNumber(depositData.merchantPayments[0].agxSalesAmount)}円` : ''}
                      </div>
                      <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                        {depositData.merchantPayments?.[0] && depositData.merchantPayments[0].agxTotalFee !== null && depositData.merchantPayments[0].agxTotalFee !== undefined ? `${formatNumber(depositData.merchantPayments[0].agxTotalFee)}円` : ''}
                      </div>
                      <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                        {depositData.merchantPayments?.[0] ? `(${formatNumber((depositData.merchantPayments[0].agxSettlementCompanyTax || 0) + (depositData.merchantPayments[0].agxInHouseTax || 0))})円` : ''}
                      </div>
                      {showMonthlyFee && (
                        <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                          {depositData.total !== null && depositData.total !== undefined ? `${formatNumber(depositData.total)}円` : ''}
                        </div>
                      )}
                      <div className="py-3 px-0 text-center bg-white flex-[1] xl:px-2">
                        {depositData.merchantPayments?.[0] && depositData.merchantPayments[0].agxPaymentAmount !== null && depositData.merchantPayments[0].agxPaymentAmount !== undefined ? `${formatNumber(depositData.merchantPayments[0].agxPaymentAmount)}円` : ''}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-4 text-[#6F6F6E] text-lg px-8">
                  <div>※毎月初の振込は、売上金額より月額費用を差し引いております。売上金額が月額費用未満であった場合、</div>
                  <div>&nbsp;&nbsp;&nbsp;&nbsp;別途月額費用を弊社宛てにお支払いいただく必要があります。</div>
                </div>
              </div>

              {/* Bank Info */}
              <div className='px-8 pb-4'>
                {isNewFormat && (
                  <div className="mb-4">
                    <div className="text-[#6F6F6E] text-lg">適格事業者登録番号</div>
                    <div className="text-[#6F6F6E] text-lg">(T6120001228218)</div>
                  </div>
                )}

                <div className="mb-2 text-[#6F6F6E] text-lg">振込先金融機関</div>
                <div className="text-lg space-y-1">
                  <div className="flex">
                    <span className="w-24">金融機関名</span>
                    <span className="mr-2">:</span>
                    <span>{depositData.merchantPayments?.[0]?.agxBankName || ''}</span>
                  </div>
                  <div className="flex">
                    <span className="w-24">支店名</span>
                    <span className="mr-2">:</span>
                    <span>{depositData.merchantPayments?.[0]?.agxBranchName || ''}</span>
                  </div>
                  <div className="flex">
                    <span className="w-24">口座種別</span>
                    <span className="mr-2">:</span>
                    <span>{depositData.merchantPayments?.[0]?.agxAcccountType || ''}</span>
                  </div>
                  <div className="flex">
                    <span className="w-24">口座番号</span>
                    <span className="mr-2">:</span>
                    <span>{depositData.merchantPayments?.[0]?.agxAccountNo || ''}</span>
                  </div>
                  <div className="flex">
                    <span className="w-24">名義人</span>
                    <span className="mr-2">:</span>
                    <span>{depositData.merchantPayments?.[0]?.agxAccountHolder || ''}</span>
                  </div>
                </div>
              </div>
            </div>



            {/* Breakdown Table */}
            <div className="overflow-x-auto">
              <div className="text-2xl text-[#6F6F6E] px-4 pb-4">決済種別ごとのデータ</div>
              <Table className="text-xl">
                <TableHeader>
                  <TableRow className='border-none'>
                    <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-medium w-[40%]">
                      <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">
                        決済種別
                      </span>
                    </TableHead>
                    <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-medium">
                      <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">
                        売上件数
                      </span>
                    </TableHead>
                    <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-medium">
                      <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">
                        売上金額
                      </span>
                    </TableHead>
                    <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-medium">
                      <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">
                        手数料率
                      </span>
                    </TableHead>
                    <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-medium">
                      <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">
                        手数料額
                      </span>
                    </TableHead>
                    <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-medium">
                      <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">
                        （内消費税）
                      </span>
                    </TableHead>
                    <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-medium">
                      <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">
                        {type === STORE.PAYGATE ? '振込金額' : '振込額'}
                      </span>
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {(() => {
                    if (!depositData.paymentBreakdowns) return null;

                    // Group payment breakdowns by category
                    const creditCardItems = depositData.paymentBreakdowns.filter(item =>
                      isCredictCardTransaction(item.agxTransactionType)
                    );
                    const qrCodeItems = depositData.paymentBreakdowns.filter(item =>
                      isQRCodeTransaction(item.agxTransactionType)
                    );
                    const electronicMoneyItems = depositData.paymentBreakdowns.filter(item =>
                      isElectronicMoneyTransaction(item.agxTransactionType)
                    );

                    const renderCategoryGroup = (categoryName: string, items: any[],isBorderTop: boolean = true) => {
                      if (items.length === 0) return null;

                      return (
                        <>
                          {/* Category header row */}
                          <TableRow key={`${categoryName}-header`} className={`${isBorderTop ? 'border-t border-[#6F6F6E]' : 'border-none'} border-b-0`}>
                            <TableCell className="py-3 px-2 text-left text-black font-medium">
                              {categoryName}
                            </TableCell>
                            <TableCell className="py-3 px-2"></TableCell>
                            <TableCell className="py-3 px-2"></TableCell>
                            <TableCell className="py-3 px-2"></TableCell>
                            <TableCell className="py-3 px-2"></TableCell>
                            <TableCell className="py-3 px-2"></TableCell>
                            <TableCell className="py-3 px-2"></TableCell>
                          </TableRow>
                          {/* Category items */}
                          {items.map((item, index) => (
                            <TableRow
                              key={`${categoryName}-${index}`}
                              className={`border-none ${index % 2 !== 0 ? 'bg-gray-100 hover:bg-gray-100' : 'hover:bg-white'}`}
                            >
                              <TableCell className="py-3 px-2 text-left pl-6">
                                <div className="flex items-center gap-2">
                                  <Link
                                    to={`/store/deposit/${type}/detail/${btoa(agxMerchantNo)}/${btoa(item.agxPaymentBreakdownId)}/${item.agxTransactionType}/${transferDate}`}
                                    className="text-[#1D9987] hover:text-[#1D9987]/80"
                                  >
                                    {`${mapTransactionType.get(item.agxTransactionType)}${item.groupCodeName ? ` (${item.groupCodeName})` : ''}`}
                                  </Link>
                                  {/* Display payment method images */}
                                  <div className="flex gap-1 ml-2">
                                    {mapTransactionImage.get(item.agxTransactionType)?.map((iconSrc, iconIndex) => (
                                      <img
                                        key={iconIndex}
                                        src={iconSrc}
                                        alt={`Payment method ${iconIndex + 1}`}
                                        className="ml-1"
                                      />
                                    ))}
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell className="py-3 px-2 text-center">
                                {item.agxNumberOfSales !== null && item.agxNumberOfSales !== undefined ? `${formatNumber(item.agxNumberOfSales)}件` : ''}
                              </TableCell>
                              <TableCell className="py-3 px-2 text-center">
                                {item.agxSalesAmount !== null && item.agxSalesAmount !== undefined ? `${formatNumber(item.agxSalesAmount)}円` : ''}
                              </TableCell>
                              <TableCell className="py-3 px-2 text-center">
                                {item.agxTotalFeeRate !== null && item.agxTotalFeeRate !== undefined ? `${Number(item.agxTotalFeeRate).toFixed(2)}%` : ''}
                              </TableCell>
                              <TableCell className="py-3 px-2 text-center">
                                {item.agxTotalFee !== null && item.agxTotalFee !== undefined ? `${formatNumber(item.agxTotalFee)}円` : ''}
                              </TableCell>
                              <TableCell className="py-3 px-2 text-center">
                                {item.tax !== null && item.tax !== undefined ? `(${formatNumber(item.tax)})円` : ''}
                              </TableCell>
                              <TableCell className="py-3 px-2 text-center">
                                {item.agxPaymentAmount !== null && item.agxPaymentAmount !== undefined ? `${formatNumber(item.agxPaymentAmount)}円` : ''}
                              </TableCell>
                            </TableRow>
                          ))}
                        </>
                      );
                    };

                    return (
                      <>
                        {renderCategoryGroup('クレジットカード', creditCardItems,false)}
                        {renderCategoryGroup('QRコード決済', qrCodeItems)}
                        {renderCategoryGroup('電子マネー', electronicMoneyItems)}
                      </>
                    );
                  })()}
                </TableBody>
              </Table>
            </div>
            {/* Tax Summary Table (for new format) */}
            <TaxSummaryTable
              data={{
                subTotalNonTax: depositData.subTotalNonTax,
                subTotalInclTax10: depositData.subTotalInclTax10,
                subTotalConsumptionTax: depositData.subTotalConsumptionTax,
                subTotalTaxIncl: depositData.subTotalTaxIncl
              }}
              transferDate={transferDate}
              switchLayoutDate={switchLayoutDate}
            />

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Sales Count Chart */}
              <div className="w-full p-4">
                <h3 className="text-xl font-semibold text-center mb-2 text-gray-500">
                  売上件数
                </h3>
                {/* <p className="text-center text-base text-gray-500 mb-2">
                    合計: {formatNumber(depositData.merchantPayments?.[0]?.agxNumberOfSales || 0)}
                  </p> */}
                <div className="h-96">
                  <Doughnut
                    data={createChartData(chartData.labelsNumberOfSales, chartData.percentageNumberOfSales)}
                    options={CHART_OPTIONS}
                    plugins={createPlugins(depositData.merchantPayments?.[0]?.agxNumberOfSales || 0)}
                  />
                </div>
              </div>

              {/* Sales Amount Chart */}
              <div className="w-full p-4">
                <h3 className="text-xl font-semibold text-center mb-2 text-gray-500">
                  売上金額
                </h3>
                {/* <p className="text-center text-base text-gray-500 mb-2">
                    合計: {formatNumber(depositData.merchantPayments?.[0]?.agxSalesAmount || 0)}円
                  </p> */}
                <div className="h-96">
                  <Doughnut
                    data={createChartData(chartData.labelsSalesAmount, chartData.percentageSalesAmount)}
                    options={CHART_OPTIONS}
                    plugins={createPlugins(depositData.merchantPayments?.[0]?.agxSalesAmount || 0)}
                  />
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};
